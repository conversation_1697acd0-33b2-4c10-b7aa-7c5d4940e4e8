<?php
/**
 * Recent Reports API for Post Activity Report System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

require_once '../config/database.php';

class RecentReportsAPI {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function getRecentReports($limit = 10) {
        try {
            $query = "SELECT 
                        ar.id,
                        ar.course_title,
                        ar.course_code,
                        ar.date_start,
                        ar.created_at,
                        ar.updated_at,
                        rgl.generated_pdf_path as pdf_path,
                        rgl.generation_status,
                        COUNT(uf.id) as file_count
                      FROM activity_reports ar
                      LEFT JOIN report_generation_log rgl ON ar.id = rgl.report_id
                      LEFT JOIN uploaded_files uf ON ar.id = uf.report_id
                      GROUP BY ar.id
                      ORDER BY ar.updated_at DESC
                      LIMIT ?";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $limit, PDO::PARAM_INT);
            $stmt->execute();
            
            $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Format the reports data
            $formattedReports = array_map(function($report) {
                return [
                    'id' => $report['id'],
                    'course_title' => $report['course_title'] ?: 'Untitled Report',
                    'course_code' => $report['course_code'],
                    'date_start' => $report['date_start'],
                    'created_at' => $report['created_at'],
                    'updated_at' => $report['updated_at'],
                    'pdf_path' => $report['pdf_path'],
                    'generation_status' => $report['generation_status'],
                    'file_count' => $report['file_count'],
                    'status' => $this->getReportStatus($report)
                ];
            }, $reports);
            
            echo json_encode([
                'success' => true,
                'reports' => $formattedReports
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function getReportStatus($report) {
        if ($report['generation_status'] === 'completed' && $report['pdf_path']) {
            return 'completed';
        } elseif ($report['generation_status'] === 'processing') {
            return 'processing';
        } elseif ($report['generation_status'] === 'failed') {
            return 'failed';
        } elseif ($report['file_count'] > 0) {
            return 'ready';
        } else {
            return 'draft';
        }
    }

    public function getReportDetails($reportId) {
        try {
            $query = "SELECT 
                        ar.*,
                        rgl.generated_pdf_path as pdf_path,
                        rgl.generation_status,
                        rgl.error_message,
                        COUNT(uf.id) as file_count
                      FROM activity_reports ar
                      LEFT JOIN report_generation_log rgl ON ar.id = rgl.report_id
                      LEFT JOIN uploaded_files uf ON ar.id = uf.report_id
                      WHERE ar.id = ?
                      GROUP BY ar.id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(1, $reportId);
            $stmt->execute();
            
            $report = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$report) {
                throw new Exception('Report not found');
            }

            // Get uploaded files
            $filesQuery = "SELECT * FROM uploaded_files WHERE report_id = ? ORDER BY upload_date";
            $filesStmt = $this->db->prepare($filesQuery);
            $filesStmt->bindParam(1, $reportId);
            $filesStmt->execute();
            $files = $filesStmt->fetchAll(PDO::FETCH_ASSOC);

            // Get photos
            $photosQuery = "SELECT * FROM photo_documentation WHERE report_id = ? ORDER BY display_order, id";
            $photosStmt = $this->db->prepare($photosQuery);
            $photosStmt->bindParam(1, $reportId);
            $photosStmt->execute();
            $photos = $photosStmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'report' => $report,
                'files' => $files,
                'photos' => $photos
            ]);

        } catch (Exception $e) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function deleteReport($reportId) {
        try {
            $this->db->beginTransaction();

            // Delete related records (cascade should handle this, but being explicit)
            $queries = [
                "DELETE FROM photo_documentation WHERE report_id = ?",
                "DELETE FROM extracted_data WHERE file_id IN (SELECT id FROM uploaded_files WHERE report_id = ?)",
                "DELETE FROM uploaded_files WHERE report_id = ?",
                "DELETE FROM sector_categories WHERE report_id = ?",
                "DELETE FROM report_generation_log WHERE report_id = ?",
                "DELETE FROM activity_reports WHERE id = ?"
            ];

            foreach ($queries as $query) {
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(1, $reportId);
                $stmt->execute();
            }

            $this->db->commit();

            echo json_encode([
                'success' => true,
                'message' => 'Report deleted successfully'
            ]);

        } catch (Exception $e) {
            $this->db->rollBack();
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}

// Handle the API request
$api = new RecentReportsAPI();

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['id'])) {
        $api->getReportDetails($_GET['id']);
    } else {
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        $api->getRecentReports($limit);
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $input = json_decode(file_get_contents('php://input'), true);
    if (isset($input['id'])) {
        $api->deleteReport($input['id']);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Report ID required']);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
