# Post Activity Report Generator

An automated system for generating post-activity reports from uploaded files. The system intelligently extracts data from various file formats and generates professional reports matching the DICT template format.

## Features

- **Automated File Processing**: Upload multiple file types (PDF, DOC, DOCX, images)
- **Intelligent Data Extraction**: Automatically extracts training details, objectives, participant information
- **Template-Based Reports**: Generates reports matching the exact DICT template format
- **Image Organization**: Automatically organizes and places images in the photo documentation section
- **PDF Generation**: Creates professional PDF reports ready for distribution
- **Modern Interface**: Responsive web interface with drag-and-drop file upload
- **Progress Tracking**: Real-time status updates during processing

## System Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache/Nginx with PHP support
- **PHP Extensions**: PDO, PDO_MySQL, ZIP, GD

### Optional Tools (for enhanced functionality)
- **wkhtmltopdf**: For high-quality PDF generation
- **pdftotext**: For better PDF text extraction
- **antiword**: For DOC file text extraction

## Installation

1. **Clone/Download** the project to your web server directory
2. **Configure Database**: Edit `config/database.php` with your database credentials
3. **Run Setup**: Visit `setup.php` in your browser to initialize the system
4. **Set Permissions**: Ensure `uploads/` and `generated_reports/` directories are writable

```bash
# Example for Linux/Mac
chmod 755 uploads generated_reports
```

## Usage

### 1. Upload Files
- Navigate to the main interface (`index.php`)
- Drag and drop or click to upload files:
  - **Activity Design**: PDF, DOC, DOCX files containing course information
  - **Instructions**: Guidelines and procedural documents
  - **Evaluations**: Assessment and feedback documents
  - **Photos**: Images for documentation (JPG, PNG, GIF)

### 2. Automatic Processing
The system will automatically:
- Extract text content from documents
- Parse relevant information (course title, dates, objectives, etc.)
- Organize images for photo documentation
- Populate the report template with extracted data

### 3. Generate Report
- Click "Generate Report" once files are processed
- The system creates a PDF matching the DICT template format
- Download the completed report

## File Structure

```
post-activity-report/
├── api/                    # Backend API endpoints
│   ├── upload.php         # File upload handler
│   ├── process.php        # File processing engine
│   ├── generate.php       # Report generation
│   └── recent_reports.php # Recent reports API
├── assets/                # Frontend assets
│   ├── css/style.css      # Styling
│   └── js/upload.js       # JavaScript functionality
├── config/                # Configuration files
│   └── database.php       # Database configuration
├── includes/              # PHP includes
│   ├── text_extractor.php # Text extraction utilities
│   ├── data_parser.php    # Data parsing logic
│   ├── report_template.php# Report template system
│   └── recent_reports.php # Recent reports component
├── sql/                   # Database schema
│   └── create_tables.sql  # Database setup script
├── uploads/               # Uploaded files storage
├── generated_reports/     # Generated PDF reports
├── index.php             # Main interface
├── view_report.php       # Report viewer
├── setup.php             # Setup script
└── README.md             # This file
```

## Database Schema

The system uses the following main tables:
- `activity_reports`: Main report data
- `uploaded_files`: File metadata and processing status
- `extracted_data`: Parsed information from files
- `photo_documentation`: Image organization
- `sector_categories`: Participant breakdown by sector
- `report_generation_log`: PDF generation history

## Supported File Types

### Documents
- **PDF**: Portable Document Format
- **DOC**: Microsoft Word 97-2003
- **DOCX**: Microsoft Word 2007+
- **TXT**: Plain text files

### Images
- **JPG/JPEG**: JPEG images
- **PNG**: Portable Network Graphics
- **GIF**: Graphics Interchange Format

## Data Extraction

The system intelligently extracts:
- **Course Information**: Title, code, dates, duration
- **Logistics**: Venue, resource person, platform used
- **Participants**: Target audience, attendance numbers
- **Content**: Objectives, topics covered, rationale
- **Outcomes**: Issues, recommendations, action items

## Troubleshooting

### Common Issues

1. **Upload Fails**
   - Check file size limits (default: 10MB)
   - Verify file types are supported
   - Ensure uploads directory is writable

2. **Text Extraction Issues**
   - Install pdftotext for better PDF extraction
   - Install antiword for DOC file support
   - Check file encoding and format

3. **PDF Generation Fails**
   - Install wkhtmltopdf for better results
   - Check generated_reports directory permissions
   - Review PHP error logs

4. **Database Connection Issues**
   - Verify database credentials in config/database.php
   - Ensure MySQL service is running
   - Check database user permissions

### Error Logs
Check your web server error logs for detailed error information:
- Apache: `/var/log/apache2/error.log`
- Nginx: `/var/log/nginx/error.log`
- PHP: Check `php.ini` for error_log location

## Customization

### Template Modification
Edit `includes/report_template.php` to customize:
- Report layout and styling
- Data field mappings
- Section organization

### Data Extraction Rules
Modify `includes/data_parser.php` to:
- Add new extraction patterns
- Customize field recognition
- Improve parsing accuracy

### Styling
Update `assets/css/style.css` for:
- Interface appearance
- Report formatting
- Responsive design

## Security Considerations

- Validate all uploaded files
- Sanitize extracted data
- Implement user authentication if needed
- Regular security updates
- Backup database regularly

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review PHP error logs
3. Verify system requirements
4. Test with sample files

## License

This project is developed for DICT training report automation.
