/* Post Activity Report Generator Styles */

:root {
    --primary-color: #0066cc;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-bg: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

body {
    background-color: var(--light-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header-section {
    background: linear-gradient(135deg, var(--primary-color), #004499);
    color: white;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

.logo {
    max-height: 60px;
    width: auto;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

/* Upload Area Styles */
.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    background-color: #fafbfc;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: #f0f7ff;
}

.upload-area.dragover {
    border-color: var(--success-color);
    background-color: #f0fff4;
    transform: scale(1.02);
}

.upload-content {
    pointer-events: none;
}

#fileInput {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* File List Styles */
.uploaded-files-list {
    max-height: 300px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    background-color: white;
    transition: all 0.2s ease;
}

.file-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.file-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    width: 30px;
    text-align: center;
}

.file-details h6 {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
}

.file-details small {
    color: #6c757d;
}

.file-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #cce7ff;
    color: #004085;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Processing Status */
.processing-step {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.processing-step:last-child {
    border-bottom: none;
}

.step-icon {
    width: 30px;
    text-align: center;
    margin-right: 0.75rem;
}

.step-content {
    flex-grow: 1;
}

.step-status {
    font-size: 0.8rem;
    font-weight: 600;
}

/* Recent Reports */
.report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    background-color: white;
    transition: all 0.2s ease;
}

.report-item:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    cursor: pointer;
}

.report-info h6 {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
}

.report-info small {
    color: #6c757d;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .file-status {
        align-self: flex-end;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Custom Scrollbar */
.uploaded-files-list::-webkit-scrollbar {
    width: 6px;
}

.uploaded-files-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.uploaded-files-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.uploaded-files-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
