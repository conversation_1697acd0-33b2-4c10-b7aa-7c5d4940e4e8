// Post Activity Report Generator - Upload Handler

class ReportGenerator {
    constructor() {
        this.uploadedFiles = [];
        this.currentReportId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadRecentReports();
    }

    setupEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const generateBtn = document.getElementById('generateReport');

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            this.handleFiles(files);
        });

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // Generate report button
        generateBtn.addEventListener('click', () => {
            this.generateReport();
        });
    }

    handleFiles(files) {
        if (files.length === 0) return;

        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif'
        ];

        const validFiles = Array.from(files).filter(file => {
            if (!allowedTypes.includes(file.type)) {
                this.showAlert(`File type not supported: ${file.name}`, 'warning');
                return false;
            }
            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                this.showAlert(`File too large: ${file.name}`, 'warning');
                return false;
            }
            return true;
        });

        if (validFiles.length > 0) {
            this.uploadFiles(validFiles);
        }
    }

    async uploadFiles(files) {
        const formData = new FormData();
        files.forEach(file => {
            formData.append('files[]', file);
        });

        this.showUploadProgress(true);
        this.updateProcessingStatus('Uploading files...', 'processing');

        try {
            const response = await fetch('api/upload.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentReportId = result.report_id;
                this.uploadedFiles = [...this.uploadedFiles, ...result.files];
                this.displayUploadedFiles();
                this.enableGenerateButton();
                this.updateProcessingStatus('Files uploaded successfully', 'completed');
                this.processFiles();
            } else {
                throw new Error(result.message || 'Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showAlert('Upload failed: ' + error.message, 'danger');
            this.updateProcessingStatus('Upload failed', 'error');
        } finally {
            this.showUploadProgress(false);
        }
    }

    async processFiles() {
        if (!this.currentReportId) return;

        this.updateProcessingStatus('Processing files...', 'processing');

        try {
            const response = await fetch('api/process.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    report_id: this.currentReportId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.updateProcessingStatus('Files processed successfully', 'completed');
                this.updateFileStatuses(result.processed_files);
            } else {
                throw new Error(result.message || 'Processing failed');
            }
        } catch (error) {
            console.error('Processing error:', error);
            this.updateProcessingStatus('Processing failed', 'error');
        }
    }

    async generateReport() {
        if (!this.currentReportId) return;

        const modal = new bootstrap.Modal(document.getElementById('processingModal'));
        modal.show();

        this.updateProcessingModal('Generating report...', 25);

        try {
            const response = await fetch('api/generate.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    report_id: this.currentReportId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.updateProcessingModal('Report generated successfully!', 100);
                setTimeout(() => {
                    modal.hide();
                    this.downloadReport(result.pdf_path);
                    this.loadRecentReports();
                }, 1500);
            } else {
                throw new Error(result.message || 'Report generation failed');
            }
        } catch (error) {
            console.error('Generation error:', error);
            this.updateProcessingModal('Generation failed: ' + error.message, 0);
            setTimeout(() => modal.hide(), 2000);
        }
    }

    displayUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        const filesList = container.querySelector('.uploaded-files-list');
        
        container.style.display = 'block';
        filesList.innerHTML = '';

        this.uploadedFiles.forEach(file => {
            const fileItem = this.createFileItem(file);
            filesList.appendChild(fileItem);
        });
    }

    createFileItem(file) {
        const div = document.createElement('div');
        div.className = 'file-item fade-in';
        
        const iconClass = this.getFileIcon(file.type);
        const statusClass = this.getStatusClass(file.status || 'pending');
        
        div.innerHTML = `
            <div class="file-info">
                <i class="${iconClass} file-icon"></i>
                <div class="file-details">
                    <h6>${file.name}</h6>
                    <small>${this.formatFileSize(file.size)} • ${file.type}</small>
                </div>
            </div>
            <div class="file-status">
                <span class="status-badge ${statusClass}">${file.status || 'Pending'}</span>
                <button class="btn btn-sm btn-outline-danger" onclick="reportGen.removeFile(${file.id})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        return div;
    }

    getFileIcon(type) {
        const iconMap = {
            'application/pdf': 'fas fa-file-pdf text-danger',
            'application/msword': 'fas fa-file-word text-primary',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fas fa-file-word text-primary',
            'text/plain': 'fas fa-file-alt text-secondary',
            'image/jpeg': 'fas fa-image text-success',
            'image/jpg': 'fas fa-image text-success',
            'image/png': 'fas fa-image text-success',
            'image/gif': 'fas fa-image text-success'
        };
        return iconMap[type] || 'fas fa-file text-secondary';
    }

    getStatusClass(status) {
        const statusMap = {
            'pending': 'status-pending',
            'processing': 'status-processing',
            'completed': 'status-completed',
            'error': 'status-error'
        };
        return statusMap[status.toLowerCase()] || 'status-pending';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateProcessingStatus(message, status) {
        const statusContainer = document.getElementById('processingStatus');
        const iconMap = {
            'processing': 'fas fa-spinner fa-spin text-primary',
            'completed': 'fas fa-check-circle text-success',
            'error': 'fas fa-exclamation-circle text-danger'
        };
        
        statusContainer.innerHTML = `
            <div class="text-center">
                <i class="${iconMap[status] || 'fas fa-clock'} fa-2x mb-3"></i>
                <p>${message}</p>
            </div>
        `;
    }

    updateProcessingModal(message, progress) {
        document.getElementById('processingMessage').textContent = message;
        document.getElementById('processingProgress').style.width = progress + '%';
    }

    showUploadProgress(show) {
        const progressContainer = document.getElementById('uploadProgress');
        progressContainer.style.display = show ? 'block' : 'none';
    }

    enableGenerateButton() {
        const btn = document.getElementById('generateReport');
        btn.disabled = false;
        btn.classList.add('pulse');
    }

    showAlert(message, type) {
        // Create and show bootstrap alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    downloadReport(pdfPath) {
        const link = document.createElement('a');
        link.href = pdfPath;
        link.download = 'post-activity-report.pdf';
        link.click();
    }

    async loadRecentReports() {
        try {
            const response = await fetch('api/recent_reports.php');
            const result = await response.json();
            
            if (result.success) {
                this.displayRecentReports(result.reports);
            }
        } catch (error) {
            console.error('Error loading recent reports:', error);
        }
    }

    displayRecentReports(reports) {
        const container = document.getElementById('recentReports');
        
        if (reports.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No recent reports</p>';
            return;
        }
        
        container.innerHTML = reports.map(report => `
            <div class="report-item" onclick="reportGen.viewReport(${report.id})">
                <div class="report-info">
                    <h6>${report.course_title || 'Untitled Report'}</h6>
                    <small>${new Date(report.created_at).toLocaleDateString()}</small>
                </div>
                <div class="report-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); reportGen.downloadReport('${report.pdf_path}')">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    viewReport(reportId) {
        window.open(`view_report.php?id=${reportId}`, '_blank');
    }

    removeFile(fileId) {
        // Implementation for removing uploaded files
        console.log('Remove file:', fileId);
    }
}

// Initialize the report generator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.reportGen = new ReportGenerator();
});
