-- Database schema for Post Activity Report System

CREATE DATABASE IF NOT EXISTS post_activity_reports;
USE post_activity_reports;

-- Table for storing activity reports
CREATE TABLE IF NOT EXISTS activity_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_title VARCHAR(255),
    course_code VARCHAR(100),
    date_start DATE,
    date_end DATE,
    time_start TIME,
    time_end TIME,
    duration VARCHAR(100),
    venue TEXT,
    resource_person VARCHAR(255),
    platform_used VARCHAR(100),
    mode VARCHAR(50),
    target_participants VARCHAR(255),
    total_attendees INT,
    male_attendees INT,
    female_attendees INT,
    beneficiaries_sex_disaggregation INT,
    rationale TEXT,
    objectives TEXT,
    topics_covered TEXT,
    issues_concerns TEXT,
    recommendations TEXT,
    plans_action_items TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for sector categories breakdown
CREATE TABLE IF NOT EXISTS sector_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    category_type ENUM('NGA', 'LGU', 'SUC', 'Others'),
    total_count INT,
    male_count INT,
    female_count INT,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);

-- Table for uploaded files
CREATE TABLE IF NOT EXISTS uploaded_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_type VARCHAR(100),
    file_size INT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);

-- Table for extracted data from files
CREATE TABLE IF NOT EXISTS extracted_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_id INT,
    data_type VARCHAR(100),
    data_key VARCHAR(255),
    data_value TEXT,
    confidence_score DECIMAL(3,2),
    extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES uploaded_files(id) ON DELETE CASCADE
);

-- Table for photo documentation
CREATE TABLE IF NOT EXISTS photo_documentation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    photo_path VARCHAR(500),
    photo_caption VARCHAR(255),
    photo_date DATE,
    display_order INT,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);

-- Table for report generation history
CREATE TABLE IF NOT EXISTS report_generation_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    report_id INT,
    generated_pdf_path VARCHAR(500),
    generation_status ENUM('pending', 'processing', 'completed', 'failed'),
    error_message TEXT,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES activity_reports(id) ON DELETE CASCADE
);
