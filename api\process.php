<?php
/**
 * File Processing Engine for Post Activity Report System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../includes/text_extractor.php';
require_once '../includes/data_parser.php';

class FileProcessor {
    private $db;
    private $textExtractor;
    private $dataParser;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->textExtractor = new TextExtractor();
        $this->dataParser = new DataParser();
    }

    public function processFiles() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['report_id'])) {
                throw new Exception('Report ID is required');
            }

            $reportId = $input['report_id'];
            
            // Get all uploaded files for this report
            $files = $this->getUploadedFiles($reportId);
            
            if (empty($files)) {
                throw new Exception('No files found for processing');
            }

            $processedFiles = [];
            $extractedData = [];

            foreach ($files as $file) {
                $this->updateFileStatus($file['id'], 'processing');
                
                try {
                    $fileData = $this->processFile($file);
                    $extractedData = array_merge($extractedData, $fileData);
                    
                    $this->updateFileStatus($file['id'], 'completed');
                    $processedFiles[] = [
                        'id' => $file['id'],
                        'name' => $file['file_name'],
                        'status' => 'completed',
                        'data_extracted' => count($fileData)
                    ];
                    
                } catch (Exception $e) {
                    $this->updateFileStatus($file['id'], 'error');
                    $processedFiles[] = [
                        'id' => $file['id'],
                        'name' => $file['file_name'],
                        'status' => 'error',
                        'error' => $e->getMessage()
                    ];
                }
            }

            // Update activity report with extracted data
            $this->updateActivityReport($reportId, $extractedData);

            echo json_encode([
                'success' => true,
                'message' => 'Files processed successfully',
                'processed_files' => $processedFiles,
                'extracted_data_count' => count($extractedData)
            ]);

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function getUploadedFiles($reportId) {
        $query = "SELECT * FROM uploaded_files WHERE report_id = ? AND processed = FALSE";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function processFile($file) {
        $extractedData = [];
        $filePath = $file['file_path'];
        $fileType = $file['file_type'];
        
        // Extract text content based on file type
        $textContent = $this->textExtractor->extractText($filePath, $fileType);
        
        if (!empty($textContent)) {
            // Parse the text content for relevant data
            $parsedData = $this->dataParser->parseContent($textContent, $file['file_name']);
            
            // Save extracted data to database
            foreach ($parsedData as $dataType => $dataValue) {
                if (!empty($dataValue)) {
                    $this->saveExtractedData($file['id'], $dataType, $dataValue);
                    $extractedData[] = [
                        'type' => $dataType,
                        'value' => $dataValue,
                        'source_file' => $file['file_name']
                    ];
                }
            }
        }

        // Handle image files separately
        if (strpos($fileType, 'image') !== false) {
            $this->processImage($file);
        }

        return $extractedData;
    }

    private function processImage($file) {
        // Extract image metadata
        $imagePath = $file['file_path'];
        $imageInfo = getimagesize($imagePath);
        
        if ($imageInfo) {
            // Save photo to photo_documentation table
            $query = "INSERT INTO photo_documentation (report_id, photo_path, photo_caption, photo_date) 
                      VALUES ((SELECT report_id FROM uploaded_files WHERE id = ?), ?, ?, NOW())";
            
            $stmt = $this->db->prepare($query);
            $caption = $this->generateImageCaption($file['file_name']);
            
            $stmt->bindParam(1, $file['id']);
            $stmt->bindParam(2, $file['file_path']);
            $stmt->bindParam(3, $caption);
            $stmt->execute();
        }
    }

    private function generateImageCaption($filename) {
        // Generate caption based on filename
        $name = pathinfo($filename, PATHINFO_FILENAME);
        $name = str_replace(['_', '-'], ' ', $name);
        $name = ucwords($name);
        
        // Add context based on filename patterns
        if (stripos($name, 'day') !== false) {
            return $name;
        } elseif (stripos($name, 'group') !== false) {
            return $name;
        } else {
            return 'Activity Documentation';
        }
    }

    private function saveExtractedData($fileId, $dataType, $dataValue, $confidence = 0.8) {
        $query = "INSERT INTO extracted_data (file_id, data_type, data_key, data_value, confidence_score) 
                  VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $fileId);
        $stmt->bindParam(2, $dataType);
        $stmt->bindParam(3, $dataType); // Using data_type as key for simplicity
        $stmt->bindParam(4, $dataValue);
        $stmt->bindParam(5, $confidence);
        
        return $stmt->execute();
    }

    private function updateFileStatus($fileId, $status) {
        $query = "UPDATE uploaded_files SET processed = ? WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $processed = ($status === 'completed') ? 1 : 0;
        $stmt->bindParam(1, $processed);
        $stmt->bindParam(2, $fileId);
        $stmt->execute();
    }

    private function updateActivityReport($reportId, $extractedData) {
        // Organize extracted data by type
        $reportData = [];
        
        foreach ($extractedData as $data) {
            $type = $data['type'];
            $value = $data['value'];
            
            if (!isset($reportData[$type])) {
                $reportData[$type] = [];
            }
            $reportData[$type][] = $value;
        }

        // Build update query dynamically
        $updateFields = [];
        $updateValues = [];
        
        $fieldMapping = [
            'course_title' => 'course_title',
            'course_code' => 'course_code',
            'date' => 'date_start',
            'time' => 'time_start',
            'duration' => 'duration',
            'venue' => 'venue',
            'resource_person' => 'resource_person',
            'platform' => 'platform_used',
            'mode' => 'mode',
            'target_participants' => 'target_participants',
            'attendees' => 'total_attendees',
            'rationale' => 'rationale',
            'objectives' => 'objectives',
            'topics' => 'topics_covered',
            'issues' => 'issues_concerns',
            'recommendations' => 'recommendations',
            'action_items' => 'plans_action_items'
        ];

        foreach ($fieldMapping as $dataType => $dbField) {
            if (isset($reportData[$dataType])) {
                $updateFields[] = "$dbField = ?";
                // Join multiple values if array
                $value = is_array($reportData[$dataType]) ? 
                         implode("\n", $reportData[$dataType]) : 
                         $reportData[$dataType];
                $updateValues[] = $value;
            }
        }

        if (!empty($updateFields)) {
            $updateValues[] = $reportId;
            $query = "UPDATE activity_reports SET " . implode(', ', $updateFields) . 
                     ", updated_at = NOW() WHERE id = ?";
            
            $stmt = $this->db->prepare($query);
            $stmt->execute($updateValues);
        }
    }
}

// Handle the processing request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $processor = new FileProcessor();
    $processor->processFiles();
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
