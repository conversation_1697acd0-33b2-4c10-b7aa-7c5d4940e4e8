<?php
/**
 * Data Parser for Post Activity Report System
 * Intelligently extracts relevant information from text content
 */

class DataParser {
    
    private $patterns = [
        'course_title' => [
            '/course\s*title[:\s]+([^\n\r]+)/i',
            '/training\s*title[:\s]+([^\n\r]+)/i',
            '/activity\s*title[:\s]+([^\n\r]+)/i',
            '/program[:\s]+([^\n\r]+)/i'
        ],
        'course_code' => [
            '/course\s*code[:\s]+([A-Z0-9\-]+)/i',
            '/code[:\s]+([A-Z0-9\-]+)/i'
        ],
        'date' => [
            '/date[:\s]+([^\n\r]+)/i',
            '/when[:\s]+([^\n\r]+)/i',
            '/schedule[:\s]+([^\n\r]+)/i',
            '/\b(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\b/',
            '/\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}/i'
        ],
        'time' => [
            '/time[:\s]+([^\n\r]+)/i',
            '/\b(\d{1,2}:\d{2}\s*(?:am|pm)?)\b/i'
        ],
        'duration' => [
            '/duration[:\s]+([^\n\r]+)/i',
            '/\b(\d+\s*(?:hours?|hrs?|days?|weeks?))\b/i'
        ],
        'venue' => [
            '/venue[:\s]+([^\n\r]+)/i',
            '/location[:\s]+([^\n\r]+)/i',
            '/where[:\s]+([^\n\r]+)/i',
            '/held\s*at[:\s]+([^\n\r]+)/i'
        ],
        'resource_person' => [
            '/resource\s*person[:\s]+([^\n\r]+)/i',
            '/facilitator[:\s]+([^\n\r]+)/i',
            '/instructor[:\s]+([^\n\r]+)/i',
            '/trainer[:\s]+([^\n\r]+)/i',
            '/speaker[:\s]+([^\n\r]+)/i'
        ],
        'platform' => [
            '/platform[:\s]+([^\n\r]+)/i',
            '/using[:\s]+(zoom|teams|google\s*meet|skype|webex)/i',
            '/\b(zoom|teams|google\s*meet|skype|webex|google\s*colab)\b/i'
        ],
        'mode' => [
            '/mode[:\s]+([^\n\r]+)/i',
            '/\b(face-to-face|online|virtual|hybrid|blended)\b/i'
        ],
        'target_participants' => [
            '/target\s*participants?[:\s]+([^\n\r]+)/i',
            '/participants?[:\s]+([^\n\r]+)/i',
            '/attendees?[:\s]+([^\n\r]+)/i'
        ],
        'attendees' => [
            '/total\s*(?:number\s*of\s*)?(?:attendees?|participants?)[:\s]*(\d+)/i',
            '/(\d+)\s*(?:attendees?|participants?)/i'
        ],
        'objectives' => [
            '/objectives?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/goals?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/aims?[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
        ],
        'rationale' => [
            '/rationale[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/background[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/introduction[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
        ],
        'topics' => [
            '/topics?\s*covered[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/curriculum[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/modules?[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
        ],
        'issues' => [
            '/issues?\s*(?:and\s*concerns?)?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/challenges?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/problems?[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
        ],
        'recommendations' => [
            '/recommendations?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/suggestions?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/improvements?[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
        ],
        'action_items' => [
            '/action\s*items?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/next\s*steps?[:\s]+([^\.]+(?:\.[^\.]*)*)/i',
            '/follow\s*up[:\s]+([^\.]+(?:\.[^\.]*)*)/i'
        ]
    ];

    public function parseContent($text, $filename = '') {
        $extractedData = [];
        
        // Clean the text first
        $text = $this->cleanText($text);
        
        // Extract data using patterns
        foreach ($this->patterns as $dataType => $patterns) {
            $value = $this->extractByPatterns($text, $patterns);
            
            if (!empty($value)) {
                $extractedData[$dataType] = $this->cleanExtractedValue($value);
            }
        }

        // Try to extract additional context from filename
        $filenameData = $this->parseFilename($filename);
        $extractedData = array_merge($filenameData, $extractedData);

        // Post-process extracted data
        $extractedData = $this->postProcessData($extractedData);

        return $extractedData;
    }

    private function extractByPatterns($text, $patterns) {
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                return trim($matches[1]);
            }
        }
        return '';
    }

    private function cleanText($text) {
        // Remove excessive whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Remove special characters that might interfere with parsing
        $text = preg_replace('/[^\w\s\-\.\,\:\;\(\)\/\&\@\#\%\$\!\?]/', ' ', $text);
        
        return trim($text);
    }

    private function cleanExtractedValue($value) {
        // Clean up extracted values
        $value = trim($value);
        $value = preg_replace('/\s+/', ' ', $value);
        
        // Remove trailing punctuation that might have been captured
        $value = rtrim($value, '.,;:');
        
        return $value;
    }

    private function parseFilename($filename) {
        $data = [];
        $filename = strtolower(pathinfo($filename, PATHINFO_FILENAME));
        
        // Extract date from filename
        if (preg_match('/(\d{4}[-_]\d{1,2}[-_]\d{1,2})/', $filename, $matches)) {
            $data['date'] = str_replace(['_', '-'], '/', $matches[1]);
        }
        
        // Extract activity type from filename
        if (strpos($filename, 'training') !== false) {
            $data['activity_type'] = 'Training';
        } elseif (strpos($filename, 'workshop') !== false) {
            $data['activity_type'] = 'Workshop';
        } elseif (strpos($filename, 'seminar') !== false) {
            $data['activity_type'] = 'Seminar';
        }
        
        return $data;
    }

    private function postProcessData($data) {
        // Standardize date formats
        if (isset($data['date'])) {
            $data['date'] = $this->standardizeDate($data['date']);
        }
        
        // Standardize time formats
        if (isset($data['time'])) {
            $data['time'] = $this->standardizeTime($data['time']);
        }
        
        // Extract numeric values
        if (isset($data['attendees'])) {
            $data['attendees'] = $this->extractNumber($data['attendees']);
        }
        
        // Clean up multi-line content
        foreach (['objectives', 'rationale', 'topics', 'issues', 'recommendations', 'action_items'] as $field) {
            if (isset($data[$field])) {
                $data[$field] = $this->cleanMultilineContent($data[$field]);
            }
        }
        
        return $data;
    }

    private function standardizeDate($dateString) {
        // Try to parse various date formats
        $formats = [
            'Y-m-d', 'Y/m/d', 'm/d/Y', 'd/m/Y',
            'F j, Y', 'M j, Y', 'j F Y'
        ];
        
        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $dateString);
            if ($date !== false) {
                return $date->format('Y-m-d');
            }
        }
        
        // If no format matches, try strtotime
        $timestamp = strtotime($dateString);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }
        
        return $dateString; // Return original if can't parse
    }

    private function standardizeTime($timeString) {
        // Try to parse time and convert to standard format
        $timestamp = strtotime($timeString);
        if ($timestamp !== false) {
            return date('H:i', $timestamp);
        }
        
        return $timeString; // Return original if can't parse
    }

    private function extractNumber($string) {
        if (preg_match('/(\d+)/', $string, $matches)) {
            return intval($matches[1]);
        }
        return $string;
    }

    private function cleanMultilineContent($content) {
        // Clean up multi-line content
        $content = preg_replace('/\s+/', ' ', $content);
        $content = trim($content);
        
        // Split into sentences and clean each
        $sentences = preg_split('/[.!?]+/', $content);
        $cleanSentences = [];
        
        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (!empty($sentence) && strlen($sentence) > 10) {
                $cleanSentences[] = $sentence;
            }
        }
        
        return implode('. ', $cleanSentences);
    }

    public function extractParticipantData($text) {
        $participantData = [];
        
        // Look for participant breakdown patterns
        $patterns = [
            'male' => '/male[:\s]*(\d+)/i',
            'female' => '/female[:\s]*(\d+)/i',
            'total' => '/total[:\s]*(\d+)/i',
            'nga' => '/nga[:\s]*(\d+)/i',
            'lgu' => '/lgu[:\s]*(\d+)/i',
            'suc' => '/suc[:\s]*(\d+)/i'
        ];
        
        foreach ($patterns as $type => $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $participantData[$type] = intval($matches[1]);
            }
        }
        
        return $participantData;
    }

    public function extractTopicsList($text) {
        $topics = [];
        
        // Look for bulleted or numbered lists
        if (preg_match_all('/(?:^|\n)\s*(?:\d+\.|\*|\-|\•)\s*([^\n\r]+)/m', $text, $matches)) {
            foreach ($matches[1] as $topic) {
                $topic = trim($topic);
                if (strlen($topic) > 5) {
                    $topics[] = $topic;
                }
            }
        }
        
        return $topics;
    }
}
?>
