<?php
/**
 * Report Viewer for Post Activity Report System
 */

require_once 'config/database.php';
require_once 'includes/report_template.php';

if (!isset($_GET['id'])) {
    die('Report ID is required');
}

$reportId = $_GET['id'];

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if report exists
    $query = "SELECT * FROM activity_reports WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->bindParam(1, $reportId);
    $stmt->execute();
    
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        die('Report not found');
    }
    
    // Generate and display the report
    $template = new ReportTemplate($db);
    $htmlContent = $template->generateHTML($reportId);
    
    // Add navigation and print controls
    $htmlContent = str_replace('<body>', '<body>
    <div class="no-print" style="position: fixed; top: 10px; right: 10px; z-index: 1000; background: white; padding: 10px; border: 1px solid #ccc; border-radius: 5px;">
        <button onclick="window.print()" style="margin-right: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">
            <i class="fas fa-print"></i> Print
        </button>
        <button onclick="window.close()" style="padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">
            <i class="fas fa-times"></i> Close
        </button>
    </div>', $htmlContent);
    
    echo $htmlContent;
    
} catch (Exception $e) {
    echo '<div style="padding: 20px; text-align: center;">';
    echo '<h2>Error Loading Report</h2>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<a href="index.php">Back to Home</a>';
    echo '</div>';
}
?>
