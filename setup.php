<?php
/**
 * Setup Script for Post Activity Report System
 */

echo "<h2>Post Activity Report System - Setup</h2>";

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die('<p style="color: red;">PHP 7.4 or higher is required. Current version: ' . PHP_VERSION . '</p>');
}

echo "<h3>System Requirements Check</h3>";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'zip', 'gd'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext extension is loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext extension is missing</p>";
        $missingExtensions[] = $ext;
    }
}

if (!empty($missingExtensions)) {
    echo "<p style='color: red;'><strong>Please install missing extensions before proceeding.</strong></p>";
    exit;
}

// Check directory permissions
$directories = ['uploads', 'generated_reports'];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p style='color: green;'>✓ Created directory: $dir</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create directory: $dir</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Directory exists: $dir</p>";
    }
    
    if (is_writable($dir)) {
        echo "<p style='color: green;'>✓ Directory is writable: $dir</p>";
    } else {
        echo "<p style='color: red;'>✗ Directory is not writable: $dir</p>";
    }
}

echo "<h3>Database Setup</h3>";

try {
    // Test database connection
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p style='color: green;'>✓ Database connection successful</p>";
        
        // Run SQL setup
        $sqlFile = 'sql/create_tables.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        $db->exec($statement);
                    } catch (PDOException $e) {
                        // Ignore table already exists errors
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            echo "<p style='color: orange;'>Warning: " . $e->getMessage() . "</p>";
                        }
                    }
                }
            }
            echo "<p style='color: green;'>✓ Database tables created/verified</p>";
        } else {
            echo "<p style='color: red;'>✗ SQL file not found: $sqlFile</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database connection failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/database.php</p>";
}

echo "<h3>Optional Tools Check</h3>";

// Check for optional command-line tools
$optionalTools = [
    'wkhtmltopdf' => 'For high-quality PDF generation',
    'pdftotext' => 'For PDF text extraction',
    'antiword' => 'For DOC file text extraction'
];

foreach ($optionalTools as $tool => $description) {
    $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
    $process = proc_open(
        "$whereIsCommand $tool",
        [
            0 => ['pipe', 'r'],
            1 => ['pipe', 'w'],
            2 => ['pipe', 'w'],
        ],
        $pipes
    );
    
    if ($process !== false) {
        $stdout = stream_get_contents($pipes[1]);
        fclose($pipes[1]);
        fclose($pipes[2]);
        proc_close($process);
        
        if (!empty(trim($stdout))) {
            echo "<p style='color: green;'>✓ $tool is available - $description</p>";
        } else {
            echo "<p style='color: orange;'>○ $tool is not available - $description</p>";
        }
    }
}

echo "<h3>Configuration</h3>";

// Check configuration files
$configFiles = [
    'config/database.php' => 'Database configuration',
    'assets/css/style.css' => 'Stylesheet',
    'assets/js/upload.js' => 'JavaScript functionality'
];

foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file - $description</p>";
    } else {
        echo "<p style='color: red;'>✗ $file missing - $description</p>";
    }
}

echo "<h3>Setup Complete</h3>";
echo "<p><strong>Your Post Activity Report System is ready!</strong></p>";
echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";

echo "<h3>Usage Instructions</h3>";
echo "<ol>";
echo "<li>Upload your activity files (PDF, DOC, DOCX, images)</li>";
echo "<li>The system will automatically extract relevant information</li>";
echo "<li>Review the extracted data and generate the final report</li>";
echo "<li>Download the generated PDF report</li>";
echo "</ol>";

echo "<h3>Troubleshooting</h3>";
echo "<ul>";
echo "<li>If PDF generation fails, install wkhtmltopdf for better results</li>";
echo "<li>For text extraction issues, install pdftotext and antiword</li>";
echo "<li>Ensure upload and generated_reports directories are writable</li>";
echo "<li>Check PHP error logs for detailed error information</li>";
echo "</ul>";
?>
