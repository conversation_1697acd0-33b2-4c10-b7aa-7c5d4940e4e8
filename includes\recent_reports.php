<?php
/**
 * Recent Reports Display Component
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT 
                ar.id,
                ar.course_title,
                ar.created_at,
                rgl.generation_status,
                rgl.generated_pdf_path
              FROM activity_reports ar
              LEFT JOIN report_generation_log rgl ON ar.id = rgl.report_id
              ORDER BY ar.created_at DESC
              LIMIT 5";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $reports = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($reports)) {
        echo '<p class="text-muted text-center">No recent reports</p>';
    } else {
        foreach ($reports as $report) {
            $title = $report['course_title'] ?: 'Untitled Report';
            $date = date('M j, Y', strtotime($report['created_at']));
            $status = $report['generation_status'] ?: 'draft';
            $pdfPath = $report['generated_pdf_path'];
            
            $statusClass = '';
            $statusText = '';
            
            switch ($status) {
                case 'completed':
                    $statusClass = 'text-success';
                    $statusText = 'Completed';
                    break;
                case 'processing':
                    $statusClass = 'text-warning';
                    $statusText = 'Processing';
                    break;
                case 'failed':
                    $statusClass = 'text-danger';
                    $statusText = 'Failed';
                    break;
                default:
                    $statusClass = 'text-muted';
                    $statusText = 'Draft';
            }
            
            echo '
            <div class="report-item" onclick="viewReport(' . $report['id'] . ')">
                <div class="report-info">
                    <h6>' . htmlspecialchars($title) . '</h6>
                    <small>' . $date . ' • <span class="' . $statusClass . '">' . $statusText . '</span></small>
                </div>
                <div class="report-actions">';
            
            if ($status === 'completed' && $pdfPath) {
                echo '<button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); downloadReport(\'' . htmlspecialchars($pdfPath) . '\')">
                        <i class="fas fa-download"></i>
                      </button>';
            }
            
            echo '</div>
            </div>';
        }
    }
    
} catch (Exception $e) {
    echo '<p class="text-danger text-center">Error loading reports</p>';
}

?>

<script>
function viewReport(reportId) {
    window.open('view_report.php?id=' + reportId, '_blank');
}

function downloadReport(pdfPath) {
    const link = document.createElement('a');
    link.href = pdfPath;
    link.download = 'post-activity-report.pdf';
    link.click();
}
</script>
