<?php
/**
 * Report Template System for Post Activity Report Generator
 */

class ReportTemplate {
    private $db;
    private $reportData;
    private $photos;

    public function __construct($database) {
        $this->db = $database;
    }

    public function generateHTML($reportId) {
        // Load report data
        $this->loadReportData($reportId);
        $this->loadPhotos($reportId);
        
        // Generate HTML template
        return $this->buildHTMLTemplate();
    }

    private function loadReportData($reportId) {
        $query = "SELECT * FROM activity_reports WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->execute();
        
        $this->reportData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$this->reportData) {
            throw new Exception("Report not found");
        }

        // Load sector categories
        $query = "SELECT * FROM sector_categories WHERE report_id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->execute();
        
        $this->reportData['sectors'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function loadPhotos($reportId) {
        $query = "SELECT * FROM photo_documentation WHERE report_id = ? ORDER BY display_order, id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->execute();
        
        $this->photos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function buildHTMLTemplate() {
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>After Training Report</title>
    <style>
        ' . $this->getReportCSS() . '
    </style>
</head>
<body>
    <div class="report-container">
        ' . $this->buildHeader() . '
        ' . $this->buildTrainingDetails() . '
        ' . $this->buildRationale() . '
        ' . $this->buildObjectives() . '
        ' . $this->buildIssuesAndRecommendations() . '
        ' . $this->buildPlansAndActions() . '
        ' . $this->buildPhotoDocumentation() . '
        ' . $this->buildSignatures() . '
    </div>
</body>
</html>';

        return $html;
    }

    private function getReportCSS() {
        return '
        @page {
            margin: 1in;
            size: A4;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.2;
            margin: 0;
            padding: 0;
        }
        
        .report-container {
            max-width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        .logo {
            max-height: 60px;
            margin-bottom: 10px;
        }
        
        .dept-name {
            font-size: 10pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 15px;
        }
        
        .section {
            margin-bottom: 15px;
        }
        
        .section-title {
            background-color: #f0f0f0;
            padding: 5px;
            font-weight: bold;
            border: 1px solid #000;
            margin-bottom: 5px;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        
        .details-table td, .details-table th {
            border: 1px solid #000;
            padding: 4px;
            vertical-align: top;
        }
        
        .details-table .label {
            background-color: #f8f8f8;
            font-weight: bold;
            width: 150px;
        }
        
        .participants-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .participants-table td, .participants-table th {
            border: 1px solid #000;
            padding: 3px;
            text-align: center;
        }
        
        .participants-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .content-section {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ccc;
        }
        
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        
        .photo-item {
            text-align: center;
        }
        
        .photo-item img {
            max-width: 100%;
            height: 200px;
            object-fit: cover;
            border: 1px solid #ccc;
        }
        
        .photo-caption {
            font-size: 9pt;
            margin-top: 5px;
            font-style: italic;
        }
        
        .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-block {
            text-align: center;
            width: 45%;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }
        
        .signature-title {
            font-weight: bold;
            font-size: 10pt;
        }
        
        .signature-subtitle {
            font-size: 9pt;
            font-style: italic;
        }
        ';
    }

    private function buildHeader() {
        return '
        <div class="header">
            <img src="assets/images/dict-logo.png" alt="DICT Logo" class="logo">
            <div class="dept-name">REPUBLIC OF THE PHILIPPINES<br>DEPARTMENT OF INFORMATION AND<br>COMMUNICATIONS TECHNOLOGY</div>
            <div class="report-title">AFTER TRAINING REPORT</div>
        </div>';
    }

    private function buildTrainingDetails() {
        $data = $this->reportData;
        
        return '
        <div class="section">
            <div class="section-title">I. TRAINING DETAILS</div>
            <table class="details-table">
                <tr>
                    <td class="label">Course Title:</td>
                    <td>' . htmlspecialchars($data['course_title'] ?? '') . '</td>
                </tr>
                <tr>
                    <td class="label">Course Code:</td>
                    <td>' . htmlspecialchars($data['course_code'] ?? '') . '</td>
                </tr>
                <tr>
                    <td class="label">Date:</td>
                    <td>' . htmlspecialchars($data['date_start'] ?? '') . '</td>
                    <td class="label">Time:</td>
                    <td>' . htmlspecialchars($data['time_start'] ?? '') . '</td>
                </tr>
                <tr>
                    <td class="label">Duration:</td>
                    <td colspan="3">' . htmlspecialchars($data['duration'] ?? '') . '</td>
                </tr>
                <tr>
                    <td class="label">Venue:</td>
                    <td colspan="3">' . htmlspecialchars($data['venue'] ?? '') . '</td>
                </tr>
                <tr>
                    <td class="label">Resource Person:</td>
                    <td colspan="3">' . htmlspecialchars($data['resource_person'] ?? '') . '</td>
                </tr>
                <tr>
                    <td class="label">Platform Used:</td>
                    <td>' . htmlspecialchars($data['platform_used'] ?? 'Google Colab') . '</td>
                    <td class="label">Mode:</td>
                    <td>' . htmlspecialchars($data['mode'] ?? 'Face-to-face') . '</td>
                </tr>
                <tr>
                    <td class="label">Target Participants:</td>
                    <td colspan="3">' . htmlspecialchars($data['target_participants'] ?? 'DICT Personnel') . '</td>
                </tr>
            </table>
            
            ' . $this->buildParticipantsTable() . '
        </div>';
    }

    private function buildParticipantsTable() {
        $data = $this->reportData;
        $sectors = $data['sectors'] ?? [];
        
        // Calculate totals
        $totalAttendees = $data['total_attendees'] ?? 0;
        $maleTotal = $data['male_attendees'] ?? 0;
        $femaleTotal = $data['female_attendees'] ?? 0;
        
        $html = '
        <table class="participants-table">
            <tr>
                <td rowspan="2" class="label">Total # of Attendees:<br><em>(with/without submitted Evaluation Form/Output)</em></td>
                <td>' . $totalAttendees . '</td>
                <td>Male</td>
                <td>' . $maleTotal . '</td>
                <td>Female</td>
                <td>' . $femaleTotal . '</td>
            </tr>
            <tr>
                <td colspan="5">Number of Beneficiaries with Sex Disaggregation</td>
            </tr>
        </table>
        
        <table class="participants-table" style="margin-top: 10px;">
            <tr>
                <td rowspan="5" class="label">Sector Category:</td>
                <td>NGA:</td>
                <td>' . $this->getSectorCount('NGA', 'total') . '</td>
                <td>Male</td>
                <td>' . $this->getSectorCount('NGA', 'male') . '</td>
                <td>Female</td>
                <td>' . $this->getSectorCount('NGA', 'female') . '</td>
            </tr>
            <tr>
                <td>LGU:</td>
                <td>' . $this->getSectorCount('LGU', 'total') . '</td>
                <td>Male</td>
                <td>' . $this->getSectorCount('LGU', 'male') . '</td>
                <td>Female</td>
                <td>' . $this->getSectorCount('LGU', 'female') . '</td>
            </tr>
            <tr>
                <td>SUC:</td>
                <td>' . $this->getSectorCount('SUC', 'total') . '</td>
                <td>Male</td>
                <td>' . $this->getSectorCount('SUC', 'male') . '</td>
                <td>Female</td>
                <td>' . $this->getSectorCount('SUC', 'female') . '</td>
            </tr>
            <tr>
                <td>Others:</td>
                <td>' . $this->getSectorCount('Others', 'total') . '</td>
                <td>Male</td>
                <td>' . $this->getSectorCount('Others', 'male') . '</td>
                <td>Female</td>
                <td>' . $this->getSectorCount('Others', 'female') . '</td>
            </tr>
            <tr>
                <td class="label">Total # of Issued Certificates: <em>(with submitted Evaluation Form/Output)</em></td>
                <td>' . $totalAttendees . '</td>
                <td>Male</td>
                <td>' . $maleTotal . '</td>
                <td>Female</td>
                <td>' . $femaleTotal . '</td>
            </tr>
        </table>';
        
        return $html;
    }

    private function getSectorCount($sectorType, $countType) {
        $sectors = $this->reportData['sectors'] ?? [];
        
        foreach ($sectors as $sector) {
            if ($sector['category_type'] === $sectorType) {
                switch ($countType) {
                    case 'total':
                        return $sector['total_count'] ?? 0;
                    case 'male':
                        return $sector['male_count'] ?? 0;
                    case 'female':
                        return $sector['female_count'] ?? 0;
                }
            }
        }
        
        return 0;
    }

    private function buildRationale() {
        $rationale = $this->reportData['rationale'] ?? '';
        
        return '
        <div class="section">
            <div class="section-title">II. RATIONALE</div>
            <div class="content-section">
                ' . nl2br(htmlspecialchars($rationale)) . '
            </div>
        </div>';
    }

    private function buildObjectives() {
        $objectives = $this->reportData['objectives'] ?? '';
        $topics = $this->reportData['topics_covered'] ?? '';
        
        return '
        <div class="section">
            <div class="section-title">III. OBJECTIVES</div>
            <div class="content-section">
                <strong>Upon completion of the course, participants will be able to learn:</strong><br><br>
                ' . nl2br(htmlspecialchars($objectives)) . '
            </div>
            
            <div style="margin-top: 15px;">
                <strong>Topics Covered:</strong><br>
                <div class="content-section">
                    ' . nl2br(htmlspecialchars($topics)) . '
                </div>
            </div>
        </div>';
    }

    private function buildIssuesAndRecommendations() {
        $issues = $this->reportData['issues_concerns'] ?? '';
        $recommendations = $this->reportData['recommendations'] ?? '';
        
        return '
        <div class="section">
            <div class="section-title">V. ISSUES AND CONCERNS</div>
            <div class="content-section">
                ' . nl2br(htmlspecialchars($issues)) . '
            </div>
            
            <div class="section-title" style="margin-top: 15px;">VI. RECOMMENDATION</div>
            <div class="content-section">
                ' . nl2br(htmlspecialchars($recommendations)) . '
            </div>
        </div>';
    }

    private function buildPlansAndActions() {
        $plans = $this->reportData['plans_action_items'] ?? '';
        
        return '
        <div class="section">
            <div class="section-title">VII. PLANS AND ACTION ITEMS (NEXT STEPS)</div>
            <div class="content-section">
                ' . nl2br(htmlspecialchars($plans)) . '
            </div>
        </div>';
    }

    private function buildPhotoDocumentation() {
        if (empty($this->photos)) {
            return '';
        }
        
        $html = '
        <div class="section">
            <div class="section-title">VIII. PHOTO DOCUMENTATION</div>
            <div class="photo-grid">';
        
        foreach ($this->photos as $photo) {
            $html .= '
                <div class="photo-item">
                    <img src="' . htmlspecialchars($photo['photo_path']) . '" alt="Activity Photo">
                    <div class="photo-caption">' . htmlspecialchars($photo['photo_caption']) . '</div>
                </div>';
        }
        
        $html .= '
            </div>
        </div>';
        
        return $html;
    }

    private function buildSignatures() {
        return '
        <div class="signatures">
            <div class="signature-block">
                <div>Prepared by:</div>
                <div class="signature-line"></div>
                <div class="signature-title">COURSE OFFICER:</div>
                <div class="signature-subtitle">&lt;Designation&gt;<br>Training Management Division</div>
            </div>
            
            <div class="signature-block">
                <div>Approved by:</div>
                <div class="signature-line"></div>
                <div class="signature-title">OIC CHIEF:</div>
                <div class="signature-subtitle">&lt;Designation&gt;<br>Training Management Division</div>
            </div>
        </div>';
    }
}
?>
