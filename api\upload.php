<?php
/**
 * File Upload Handler for Post Activity Report System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class FileUploadHandler {
    private $db;
    private $uploadDir = '../uploads/';
    private $allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif'
    ];
    private $maxFileSize = 10 * 1024 * 1024; // 10MB

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Create upload directory if it doesn't exist
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }

    public function handleUpload() {
        try {
            if (!isset($_FILES['files']) || empty($_FILES['files']['name'][0])) {
                throw new Exception('No files uploaded');
            }

            // Create new activity report record
            $reportId = $this->createActivityReport();
            
            $uploadedFiles = [];
            $files = $_FILES['files'];
            
            for ($i = 0; $i < count($files['name']); $i++) {
                if ($files['error'][$i] === UPLOAD_ERR_OK) {
                    $fileInfo = [
                        'name' => $files['name'][$i],
                        'type' => $files['type'][$i],
                        'tmp_name' => $files['tmp_name'][$i],
                        'size' => $files['size'][$i]
                    ];
                    
                    $uploadedFile = $this->processFile($fileInfo, $reportId);
                    if ($uploadedFile) {
                        $uploadedFiles[] = $uploadedFile;
                    }
                }
            }

            if (empty($uploadedFiles)) {
                throw new Exception('No files were successfully uploaded');
            }

            echo json_encode([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'report_id' => $reportId,
                'files' => $uploadedFiles
            ]);

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function createActivityReport() {
        $query = "INSERT INTO activity_reports (created_at) VALUES (NOW())";
        $stmt = $this->db->prepare($query);
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to create activity report record');
        }
        
        return $this->db->lastInsertId();
    }

    private function processFile($fileInfo, $reportId) {
        // Validate file
        if (!$this->validateFile($fileInfo)) {
            return null;
        }

        // Generate unique filename
        $extension = pathinfo($fileInfo['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = $this->uploadDir . $filename;

        // Move uploaded file
        if (!move_uploaded_file($fileInfo['tmp_name'], $filepath)) {
            throw new Exception('Failed to move uploaded file: ' . $fileInfo['name']);
        }

        // Save file info to database
        $fileId = $this->saveFileInfo($fileInfo, $filename, $filepath, $reportId);

        return [
            'id' => $fileId,
            'name' => $fileInfo['name'],
            'type' => $fileInfo['type'],
            'size' => $fileInfo['size'],
            'path' => $filepath,
            'status' => 'uploaded'
        ];
    }

    private function validateFile($fileInfo) {
        // Check file type
        if (!in_array($fileInfo['type'], $this->allowedTypes)) {
            error_log("Invalid file type: " . $fileInfo['type']);
            return false;
        }

        // Check file size
        if ($fileInfo['size'] > $this->maxFileSize) {
            error_log("File too large: " . $fileInfo['size']);
            return false;
        }

        // Check for upload errors
        if ($fileInfo['size'] === 0) {
            error_log("Empty file: " . $fileInfo['name']);
            return false;
        }

        return true;
    }

    private function saveFileInfo($fileInfo, $filename, $filepath, $reportId) {
        $query = "INSERT INTO uploaded_files (report_id, file_name, file_path, file_type, file_size, upload_date) 
                  VALUES (?, ?, ?, ?, ?, NOW())";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->bindParam(2, $fileInfo['name']);
        $stmt->bindParam(3, $filepath);
        $stmt->bindParam(4, $fileInfo['type']);
        $stmt->bindParam(5, $fileInfo['size']);

        if (!$stmt->execute()) {
            throw new Exception('Failed to save file information to database');
        }

        return $this->db->lastInsertId();
    }

    private function getFileCategory($filename, $type) {
        $filename = strtolower($filename);
        
        if (strpos($filename, 'design') !== false || strpos($filename, 'plan') !== false) {
            return 'activity_design';
        } elseif (strpos($filename, 'instruction') !== false || strpos($filename, 'guide') !== false) {
            return 'instructions';
        } elseif (strpos($filename, 'evaluation') !== false || strpos($filename, 'assessment') !== false) {
            return 'evaluation';
        } elseif (strpos($type, 'image') !== false) {
            return 'photo';
        } else {
            return 'document';
        }
    }
}

// Handle the upload request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $handler = new FileUploadHandler();
    $handler->handleUpload();
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
