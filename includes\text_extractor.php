<?php
/**
 * Text Extraction Utility for Post Activity Report System
 */

class TextExtractor {
    
    public function extractText($filePath, $fileType) {
        switch ($fileType) {
            case 'text/plain':
                return $this->extractFromText($filePath);
            
            case 'application/pdf':
                return $this->extractFromPDF($filePath);
            
            case 'application/msword':
            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return $this->extractFromWord($filePath);
            
            default:
                return '';
        }
    }

    private function extractFromText($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("File not found: $filePath");
        }
        
        return file_get_contents($filePath);
    }

    private function extractFromPDF($filePath) {
        // For PDF extraction, we'll use a simple approach
        // In production, you might want to use libraries like pdf2text or pdftotext
        
        if (!file_exists($filePath)) {
            throw new Exception("File not found: $filePath");
        }

        // Try using pdftotext command if available
        $output = '';
        $command = "pdftotext '$filePath' -";
        
        if ($this->commandExists('pdftotext')) {
            $output = shell_exec($command);
        } else {
            // Fallback: Basic PDF text extraction (limited)
            $output = $this->basicPDFExtraction($filePath);
        }
        
        return $output ?: '';
    }

    private function extractFromWord($filePath) {
        if (!file_exists($filePath)) {
            throw new Exception("File not found: $filePath");
        }

        $text = '';
        
        // For .docx files
        if (pathinfo($filePath, PATHINFO_EXTENSION) === 'docx') {
            $text = $this->extractFromDocx($filePath);
        } else {
            // For .doc files, try using antiword if available
            if ($this->commandExists('antiword')) {
                $text = shell_exec("antiword '$filePath'");
            }
        }
        
        return $text ?: '';
    }

    private function extractFromDocx($filePath) {
        $text = '';
        
        try {
            $zip = new ZipArchive();
            
            if ($zip->open($filePath) === TRUE) {
                $xml = $zip->getFromName('word/document.xml');
                
                if ($xml !== false) {
                    $dom = new DOMDocument();
                    $dom->loadXML($xml);
                    
                    // Extract text from all text nodes
                    $xpath = new DOMXPath($dom);
                    $textNodes = $xpath->query('//w:t');
                    
                    foreach ($textNodes as $textNode) {
                        $text .= $textNode->nodeValue . ' ';
                    }
                }
                
                $zip->close();
            }
        } catch (Exception $e) {
            error_log("Error extracting from DOCX: " . $e->getMessage());
        }
        
        return trim($text);
    }

    private function basicPDFExtraction($filePath) {
        // Very basic PDF text extraction - not reliable for all PDFs
        $content = file_get_contents($filePath);
        $text = '';
        
        // Look for text between stream and endstream
        if (preg_match_all('/stream\s*\n(.*?)\n\s*endstream/s', $content, $matches)) {
            foreach ($matches[1] as $match) {
                // Try to extract readable text
                $decoded = $this->decodeStream($match);
                if ($decoded) {
                    $text .= $decoded . "\n";
                }
            }
        }
        
        return $text;
    }

    private function decodeStream($stream) {
        // Basic stream decoding - very limited
        $text = '';
        
        // Remove binary data and extract text-like content
        if (preg_match_all('/\((.*?)\)/', $stream, $matches)) {
            foreach ($matches[1] as $match) {
                $text .= $match . ' ';
            }
        }
        
        return trim($text);
    }

    private function commandExists($command) {
        $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ['pipe', 'r'], // stdin
                1 => ['pipe', 'w'], // stdout
                2 => ['pipe', 'w'], // stderr
            ],
            $pipes
        );
        
        if ($process !== false) {
            $stdout = stream_get_contents($pipes[1]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            proc_close($process);
            
            return !empty(trim($stdout));
        }
        
        return false;
    }

    public function extractMetadata($filePath, $fileType) {
        $metadata = [];
        
        if (strpos($fileType, 'image') !== false) {
            $metadata = $this->extractImageMetadata($filePath);
        }
        
        return $metadata;
    }

    private function extractImageMetadata($filePath) {
        $metadata = [];
        
        if (function_exists('exif_read_data') && in_array(pathinfo($filePath, PATHINFO_EXTENSION), ['jpg', 'jpeg'])) {
            $exif = @exif_read_data($filePath);
            
            if ($exif !== false) {
                $metadata['date_taken'] = $exif['DateTime'] ?? null;
                $metadata['camera'] = $exif['Model'] ?? null;
                $metadata['dimensions'] = ($exif['COMPUTED']['Width'] ?? '') . 'x' . ($exif['COMPUTED']['Height'] ?? '');
            }
        }
        
        // Get basic image info
        $imageInfo = getimagesize($filePath);
        if ($imageInfo) {
            $metadata['width'] = $imageInfo[0];
            $metadata['height'] = $imageInfo[1];
            $metadata['type'] = $imageInfo['mime'];
        }
        
        return $metadata;
    }

    public function cleanText($text) {
        // Clean and normalize extracted text
        $text = preg_replace('/\s+/', ' ', $text); // Normalize whitespace
        $text = trim($text);
        $text = mb_convert_encoding($text, 'UTF-8', 'auto'); // Ensure UTF-8 encoding
        
        return $text;
    }
}
?>
