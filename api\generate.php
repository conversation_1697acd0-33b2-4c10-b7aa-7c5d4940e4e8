<?php
/**
 * Report Generation API for Post Activity Report System
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../includes/report_template.php';

class ReportGenerator {
    private $db;
    private $outputDir = '../generated_reports/';

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Create output directory if it doesn't exist
        if (!file_exists($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
    }

    public function generateReport() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['report_id'])) {
                throw new Exception('Report ID is required');
            }

            $reportId = $input['report_id'];
            
            // Update generation status
            $this->updateGenerationStatus($reportId, 'processing');
            
            // Generate HTML template
            $template = new ReportTemplate($this->db);
            $htmlContent = $template->generateHTML($reportId);
            
            // Generate PDF
            $pdfPath = $this->generatePDF($htmlContent, $reportId);
            
            // Update generation status
            $this->updateGenerationStatus($reportId, 'completed', $pdfPath);
            
            echo json_encode([
                'success' => true,
                'message' => 'Report generated successfully',
                'pdf_path' => $pdfPath,
                'html_preview' => $htmlContent
            ]);

        } catch (Exception $e) {
            $this->updateGenerationStatus($reportId ?? null, 'failed', null, $e->getMessage());
            
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    private function generatePDF($htmlContent, $reportId) {
        // For PDF generation, we'll use multiple approaches based on availability
        
        // Method 1: Try using wkhtmltopdf if available
        if ($this->commandExists('wkhtmltopdf')) {
            return $this->generatePDFWithWkhtmltopdf($htmlContent, $reportId);
        }
        
        // Method 2: Use DomPDF library (requires installation)
        if (class_exists('Dompdf\Dompdf')) {
            return $this->generatePDFWithDompdf($htmlContent, $reportId);
        }
        
        // Method 3: Fallback - save as HTML with print styles
        return $this->generateHTMLForPrint($htmlContent, $reportId);
    }

    private function generatePDFWithWkhtmltopdf($htmlContent, $reportId) {
        $tempHtmlFile = tempnam(sys_get_temp_dir(), 'report_') . '.html';
        $pdfFileName = 'report_' . $reportId . '_' . time() . '.pdf';
        $pdfPath = $this->outputDir . $pdfFileName;
        
        // Write HTML to temporary file
        file_put_contents($tempHtmlFile, $htmlContent);
        
        // Generate PDF using wkhtmltopdf
        $command = "wkhtmltopdf --page-size A4 --margin-top 1in --margin-bottom 1in --margin-left 1in --margin-right 1in '$tempHtmlFile' '$pdfPath'";
        
        $output = shell_exec($command . ' 2>&1');
        
        // Clean up temporary file
        unlink($tempHtmlFile);
        
        if (!file_exists($pdfPath)) {
            throw new Exception('PDF generation failed: ' . $output);
        }
        
        return $pdfPath;
    }

    private function generatePDFWithDompdf($htmlContent, $reportId) {
        require_once '../vendor/autoload.php'; // Assuming Composer autoload
        
        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($htmlContent);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        
        $pdfFileName = 'report_' . $reportId . '_' . time() . '.pdf';
        $pdfPath = $this->outputDir . $pdfFileName;
        
        file_put_contents($pdfPath, $dompdf->output());
        
        return $pdfPath;
    }

    private function generateHTMLForPrint($htmlContent, $reportId) {
        // Add print-specific styles
        $printStyles = '
        <style media="print">
            @page {
                margin: 1in;
                size: A4;
            }
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .no-print {
                display: none !important;
            }
        </style>';
        
        $htmlContent = str_replace('</head>', $printStyles . '</head>', $htmlContent);
        
        $htmlFileName = 'report_' . $reportId . '_' . time() . '.html';
        $htmlPath = $this->outputDir . $htmlFileName;
        
        file_put_contents($htmlPath, $htmlContent);
        
        return $htmlPath;
    }

    private function commandExists($command) {
        $whereIsCommand = (PHP_OS == 'WINNT') ? 'where' : 'which';
        $process = proc_open(
            "$whereIsCommand $command",
            [
                0 => ['pipe', 'r'],
                1 => ['pipe', 'w'],
                2 => ['pipe', 'w'],
            ],
            $pipes
        );
        
        if ($process !== false) {
            $stdout = stream_get_contents($pipes[1]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            proc_close($process);
            
            return !empty(trim($stdout));
        }
        
        return false;
    }

    private function updateGenerationStatus($reportId, $status, $pdfPath = null, $errorMessage = null) {
        if (!$reportId) return;
        
        $query = "INSERT INTO report_generation_log (report_id, generated_pdf_path, generation_status, error_message) 
                  VALUES (?, ?, ?, ?) 
                  ON DUPLICATE KEY UPDATE 
                  generated_pdf_path = VALUES(generated_pdf_path),
                  generation_status = VALUES(generation_status),
                  error_message = VALUES(error_message),
                  generated_at = NOW()";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->bindParam(2, $pdfPath);
        $stmt->bindParam(3, $status);
        $stmt->bindParam(4, $errorMessage);
        $stmt->execute();
    }

    public function getReportStatus($reportId) {
        $query = "SELECT * FROM report_generation_log WHERE report_id = ? ORDER BY generated_at DESC LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $reportId);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}

// Handle the generation request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $generator = new ReportGenerator();
    $generator->generateReport();
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['status'])) {
    $generator = new ReportGenerator();
    $status = $generator->getReportStatus($_GET['report_id']);
    echo json_encode(['success' => true, 'status' => $status]);
} else {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
